<html>
  <head>
    <title>Chess Openings Trainer</title>

    <link rel="shortcut icon" href="/static/favicon.ico">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <script src="/static/js/chess.js"></script>

    <link rel="stylesheet" href="/static/css/chessboard-1.0.0.min.css">
    <script src="/static/js/chessboard-1.0.0.min.js"></script>

    <style>
      .trainer-controls {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .start-training-btn {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 15px 30px;
        border-radius: 8px;
        width: 100%;
        margin-top: 20px;
      }
      .orientation-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .control-group {
        margin-bottom: 15px;
      }
      .control-label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
      }

      /* Timer freeze animation */
      .timer-frozen {
        animation: pulse-freeze 1.5s infinite;
        border: 2px solid #ffc107;
        border-radius: 8px;
        padding: 5px;
        background-color: #fff3cd;
      }

      @keyframes pulse-freeze {
        0% { opacity: 1; }
        50% { opacity: 0.6; }
        100% { opacity: 1; }
      }

      .timer-unfreezing {
        animation: unfreeze-flash 0.8s ease-out;
      }

      @keyframes unfreeze-flash {
        0% { background-color: #d4edda; transform: scale(1.05); }
        50% { background-color: #c3e6cb; transform: scale(1.1); }
        100% { background-color: transparent; transform: scale(1); }
      }

      /* Progress indicator styling */
      #progress-indicator {
        background-color: #f8f9fa;
        border: 2px solid #007bff;
        border-radius: 8px;
        padding: 15px;
        margin: 0 auto;
        max-width: 400px;
      }

      .progress-text {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #007bff;
      }

      .progress-bar-container {
        background-color: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin-bottom: 8px;
      }

      .progress-bar-fill {
        background: linear-gradient(90deg, #28a745, #20c997);
        height: 100%;
        width: 0%;
        transition: width 0.1s ease-out;
        border-radius: 10px;
      }

      .progress-time {
        font-size: 0.9rem;
        color: #6c757d;
      }

      .board-container {
        position: relative;
      }

      .board-disabled {
        pointer-events: none;
      }

      /* Mobile responsiveness - Force stacking on small screens */
      @media (max-width: 900px) {
        /* Force the row to be a flex column on mobile */
        .card-body .row {
          flex-direction: column !important;
        }

        /* Force columns to stack vertically and take full width */
        .card-body .row .chess-board-column,
        .card-body .row .controls-column {
          flex: 0 0 100% !important;
          max-width: 100% !important;
          width: 100% !important;
        }

        /* Stack chess board and controls vertically on mobile */
        .chess-board-column {
          order: 1;
          margin-bottom: 20px;
        }

        .controls-column {
          order: 2;
        }

        /* Adjust chess board size for mobile */
        #chess_board {
          width: 100% !important;
          max-width: 500px !important;
        }

        /* Make trainer controls more compact on mobile */
        .trainer-controls {
          padding: 15px;
          margin-bottom: 15px;
        }

        .start-training-btn {
          padding: 12px 20px;
          font-size: 1.1rem;
        }

        /* Adjust control groups spacing */
        .control-group {
          margin-bottom: 12px;
        }

        /* Make orientation toggle more mobile-friendly */
        .orientation-toggle {
          flex-direction: column;
          gap: 8px;
          align-items: stretch;
        }

        .orientation-toggle .btn-group {
          width: 100%;
        }

        .orientation-toggle .btn {
          flex: 1;
        }

        /* Adjust training session stats for mobile */
        .row.mb-3 .col-6 {
          margin-bottom: 10px;
        }

        /* Make progress indicator more compact on mobile */
        #progress-indicator {
          padding: 10px;
          max-width: 350px;
        }

        .progress-text {
          font-size: 1rem;
        }

        .progress-time {
          font-size: 0.8rem;
        }
      }

      /* Extra small devices (phones in portrait) */
      @media (max-width: 575.98px) {
        .container-fluid {
          padding-left: 10px;
          padding-right: 10px;
        }

        .card {
          margin-top: 10px;
        }

        .card-body {
          padding: 15px;
        }

        #chess_board {
          max-width: 400px !important;
        }

        .trainer-controls {
          padding: 12px;
        }

        .start-training-btn {
          padding: 10px 15px;
          font-size: 1rem;
        }

        /* Make form controls more touch-friendly */
        .form-control, .btn {
          min-height: 44px;
        }

        /* Adjust hint modal for very small screens */
        #hint-moves {
          font-size: 1rem;
          word-break: break-word;
        }

        /* Make FEN input smaller on mobile */
        #hint-fen-display {
          font-size: 0.75rem !important;
        }
      }

      /* FEN copy section styling */
      #hint-fen-display {
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        font-size: 0.85rem;
      }

      #copy-fen-button {
        white-space: nowrap;
      }

      /* Notification styling */
      #fen-copied-notification,
      #fen-copy-failed-notification {
        border-radius: 4px;
        padding: 8px;
        margin-top: 5px;
      }

      #fen-copied-notification {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
      }

      #fen-copy-failed-notification {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
      }
    </style>

  </head>
  <body>
    <div class="container-fluid">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-11 col-xl-10">
          <div class="card mt-3">
            <div class="card-header bg-primary text-white">
              <h3 class="mb-0 text-center">Chess Openings Trainer</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Chess Board Column -->
                <div class="col-12 col-md-7 mb-4 mb-md-0 chess-board-column">
                  <div class="board-container">
                    <div id="chess_board" class="mx-auto mb-3" style="width: 480px;"></div>
                  </div>

                  <!-- Progress Indicator (moved below chess board) -->
                  <div id="progress-indicator" class="text-center mb-3" style="display: none;">
                    <div class="progress-text">🤖 Engine is thinking...</div>
                    <div class="progress-bar-container">
                      <div id="progress-bar-fill" class="progress-bar-fill"></div>
                    </div>
                    <div id="progress-time" class="progress-time">0.0s</div>
                  </div>

                  <div class="text-center">
                    <strong><div id="status" class="mb-3"></div></strong>
                  </div>
                </div>

                <!-- Controls Column -->
                <div class="col-12 col-md-5 controls-column">
                  <!-- Training Settings (shown when not training) -->
                  <div id="training-settings" class="trainer-controls">
                    <h5 class="mb-3 text-center">Training Settings</h5>

                    <!-- Board Orientation Toggle -->
                    <div class="control-group">
                      <div class="control-label">Board Orientation</div>
                      <div class="orientation-toggle">
                        <span>Play as:</span>
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                          <label class="btn btn-outline-primary active">
                            <input type="radio" name="orientation" id="white_orientation" value="white" checked> White
                          </label>
                          <label class="btn btn-outline-primary">
                            <input type="radio" name="orientation" id="black_orientation" value="black"> Black
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- FEN Input -->
                    <div class="control-group">
                      <div class="control-label">Starting Position (FEN)</div>
                      <div class="input-group">
                        <input id="fen" type="text" class="form-control" placeholder="Enter FEN or leave empty for starting position">
                        <div class="input-group-append">
                          <button id="reset_board" class="btn btn-outline-warning">Reset</button>
                          <button id="set_fen" class="btn btn-outline-success">Set Position</button>
                        </div>
                      </div>
                    </div>

                    <!-- Move Time -->
                    <div class="control-group">
                      <div class="control-label">Move Time</div>
                      <select id="move_time" class="form-control">
                        <option value="instant">Instant response</option>
                        <option value="1">1 sec</option>
                        <option value="3">3 sec</option>
                        <option value="5" selected>5 sec</option>
                        <option value="10">10 sec</option>
                        <option value="15">15 sec</option>
                        <option value="30">30 sec</option>
                        <option value="60">1 minute</option>
                      </select>
                    </div>

                    <!-- Engine Selection -->
                    <div class="control-group">
                      <div class="control-label">Chess Engine</div>
                      <select id="engine" class="form-control">
                        <option value="stockfish" selected>Stockfish</option>
                        <option value="lczero">Leela Chess Zero (LC0)</option>
                      </select>
                    </div>

                    <!-- Training Level -->
                    <div class="control-group">
                      <div class="control-label">Training Level</div>
                      <select id="training_level" class="form-control">
                        <!-- Options will be populated dynamically using getLevelDisplayName -->
                      </select>
                    </div>

                    <!-- Start Training Button -->
                    <button id="start_training" class="btn btn-success start-training-btn">
                      🚀 Start Training
                    </button>
                  </div>

                  <!-- Training Session (shown when training) -->
                  <div id="training-session" class="trainer-controls" style="display: none;">
                    <h5 class="mb-3 text-center">Training Session</h5>

                    <!-- Training Stats -->
                    <div class="row mb-3">
                      <div class="col-6">
                        <div class="text-center">
                          <div class="control-label">Remaining Time</div>
                          <div id="remaining-time" class="h4 text-primary">5:00</div>
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="text-center">
                          <div class="control-label">Score</div>
                          <div id="score-display" class="h4 text-success">0</div>
                        </div>
                      </div>
                    </div>

                    <!-- Training Info -->
                    <div class="mb-3">
                      <div class="small text-muted text-center">
                        <div>Playing as: <span id="session-orientation" class="font-weight-bold"></span></div>
                        <div>Engine: <span id="session-engine" class="font-weight-bold"></span></div>
                        <div>Move Time: <span id="session-move-time" class="font-weight-bold"></span></div>
                        <div>Level: <span id="session-level" class="font-weight-bold"></span></div>
                      </div>
                    </div>

                    <!-- Surrender Button -->
                    <button id="surrender" class="btn btn-danger start-training-btn">
                      🏳️ Surrender
                    </button>
                  </div>

                  <!-- Hint Modal (shown when player makes wrong move) -->
                  <div id="hint-modal" class="trainer-controls" style="display: none; border: 3px solid #ffc107; background-color: #fff3cd;">
                    <h5 class="mb-3 text-center text-warning">💡 Hint: Optimal Moves</h5>

                    <div class="mb-3">
                      <div class="text-center">
                        <div class="control-label">The best moves were:</div>
                        <div id="hint-moves" class="h5 text-dark font-weight-bold"></div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="small text-muted text-center">
                        Study these moves and try to understand the position better.
                      </div>
                    </div>

                    <!-- FEN Copy Section -->
                    <div class="mb-3" id="fen-copy-section" style="display: none;">
                      <div class="text-center">
                        <div class="control-label">Position FEN for Analysis:</div>
                        <div class="input-group mt-2">
                          <input type="text" id="hint-fen-display" class="form-control" readonly style="font-size: 0.85rem;">
                          <div class="input-group-append">
                            <button id="copy-fen-button" class="btn btn-outline-primary btn-sm" title="Copy FEN to clipboard">
                              📋 Copy
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-3" id="fen-copied-notification" style="display: none;">
                      <div class="small text-success text-center font-weight-bold">
                        ✅ Position FEN copied to clipboard for analysis
                      </div>
                    </div>

                    <div class="mb-3" id="fen-copy-failed-notification" style="display: none;">
                      <div class="small text-danger text-center font-weight-bold">
                        ❌ Failed to copy automatically. Please use the Copy button above.
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="small text-warning text-center font-weight-bold">
                        ⚠️ 30 seconds will be deducted from your timer
                      </div>
                    </div>

                    <!-- Continue Button -->
                    <button id="continue-after-hint" class="btn btn-warning start-training-btn">
                      ✅ Continue Training (-30s)
                    </button>
                  </div>

                  <!-- Training Summary Modal (shown when training ends) -->
                  <div id="training-summary-modal" class="trainer-controls" style="display: none; border: 3px solid #17a2b8; background-color: #d1ecf1;">
                    <h5 class="mb-3 text-center text-info">📊 Training Session Summary</h5>

                    <div class="mb-3">
                      <div class="text-center">
                        <div class="control-label">Score</div>
                        <div id="summary-score" class="h4 text-success">0</div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="text-center">
                        <div class="control-label">Session Ended</div>
                        <div id="summary-end-reason" class="h5 text-dark font-weight-bold"></div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="small text-muted text-center">
                        <div>Duration: <span id="summary-duration" class="font-weight-bold"></span></div>
                        <div>Engine: <span id="summary-engine-used" class="font-weight-bold"></span></div>
                        <div>Playing as: <span id="summary-orientation-used" class="font-weight-bold"></span></div>
                        <div>Level: <span id="summary-level-used" class="font-weight-bold"></span></div>
                      </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                      <div class="col-6">
                        <button id="repeat-training" class="btn btn-success btn-block">
                          🔄 Repeat
                        </button>
                      </div>
                      <div class="col-6">
                        <button id="close-summary" class="btn btn-secondary btn-block">
                          ✖️ Close
                        </button>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  // Centralized training level configuration
  const TRAINING_LEVELS = {
    easy: { tolerance: -60, displayName: 'Easy (<60 centipawns lost)' },
    normal: { tolerance: -30, displayName: 'Normal (<30 centipawns lost)' },
    hard: { tolerance: -20, displayName: 'Hard (<20 centipawns lost)' }
  };

  // Helper function to get tolerance for a level
  function getTolerance(level) {
    return TRAINING_LEVELS[level]?.tolerance || TRAINING_LEVELS.normal.tolerance;
  }

  // Helper function to get display name for a level
  function getLevelDisplayName(level) {
    return TRAINING_LEVELS[level]?.displayName || TRAINING_LEVELS.normal.displayName;
  }

  // Filter moves based on tolerance level
  function filterMovesByTolerance(bestMoves, toleranceThreshold) {
    if (!bestMoves || bestMoves.length === 0) {
      return [];
    }

    // Filter moves that are better than or equal to the tolerance threshold
    // Since we're dealing with negative values, moves >= threshold are acceptable
    return bestMoves.filter(function(moveData) {
      var moveDiff = moveData[1]; // The second element is the difference
      return moveDiff >= toleranceThreshold;
    });
  }

  // Populate training level options dynamically
  function populateTrainingLevelOptions() {
    var select = $('#training_level');
    select.empty(); // Clear existing options

    Object.keys(TRAINING_LEVELS).forEach(function(level) {
      var option = $('<option></option>')
        .attr('value', level)
        .text(getLevelDisplayName(level));

      // Set easy as default selected
      if (level === 'easy') {
        option.attr('selected', true);
      }

      select.append(option);
    });
  }

  let currentMoveRequest = null;
  // make computer move
  function make_move() {
    trainingMove++;

    // Use training parameters if training is active, otherwise use current UI values
    let moveTimeStr = trainingActive ? trainingParams.moveTime : $('#move_time option:selected').val();
    let moveTimeFloat = (moveTimeStr === 'instant') ? 0.1 : parseFloat(moveTimeStr);
    var engineType = trainingActive ? trainingParams.engine : $('#engine option:selected').val();
    var orientation = trainingActive ? trainingParams.orientation : board.orientation();

    // Show progress overlay and start progress tracking
    showProgress(moveTimeFloat);

    // make HTTP POST request to make move API
    currentMoveRequest = $.ajax({
        type: 'POST',
        url: '/make_move',
        contentType: 'application/json',
        data: JSON.stringify({
            pgn: game.pgn(),
            move_time: moveTimeFloat,
            engine_type: engineType,
            orientation: orientation,
            training_move: trainingMove
        })
    }).done(function(data) {
        // Hide progress overlay
        hideProgress();

        // Handle new response structure with sample_move, prev_move_diff, best_prev_moves
        if (trainingActive) {

          // Scenario 2: sample_move is not None - check prev_move_diff against level tolerance
          if (data.prev_move_diff !== null && data.prev_move_diff !== undefined) {
            // Get tolerance threshold based on training level
            var toleranceThreshold = getTolerance(trainingParams.level);

            // Check if move quality exceeds tolerance (worse than threshold)
            if (data.prev_move_diff < toleranceThreshold) {
              // Move quality is not good enough - show hint with penalty
              console.log('Move quality exceeded tolerance (' + data.prev_move_diff + ' < ' + toleranceThreshold + '), showing hint');
              if (data.best_prev_moves && data.best_prev_moves.length > 0) {
                // Filter moves based on game level tolerance
                var filteredMoves = filterMovesByTolerance(data.best_prev_moves, toleranceThreshold);

                if (filteredMoves.length > 0) {
                  // Generate FEN from current game state by removing the last (wrong) move
                  var fenBeforeWrongMove = generateFenBeforeLastMove();
                  showHint(filteredMoves, fenBeforeWrongMove);
                  return; // Exit early, don't update board
                }
              } else {
                // No hints available, apply penalty and restart
                remainingTime = Math.max(0, remainingTime - 30);
                updateTimerDisplay();

                // If time runs out due to penalty, stop training
                if (remainingTime <= 0) {
                  stopTraining('Time is up!', 'timeout');
                } else {
                  // Otherwise restart the training round
                  restartTrainingRound();
                }
                return; // Exit early, don't update board
              }
            }
            // If we reach here, move quality is acceptable - continue game
          }
          // Scenario 1: sample_move is None - restart training without penalty
          if (!data.sample_move) {
            console.log('No sample move available, restarting training round');
            restartTrainingRound();
            return;
          }
        }

        // Execute the sample move (bot's next move)
        if (data.sample_move && data.sample_move.trim() !== '') {
          game.move(data.sample_move, { sloppy: true })
        }
        // update board position
        board.position(game.fen());

        // update game status
        updateStatus();

        // if training is active and we executed a move, calculate score
        if (trainingActive && data.sample_move) {
          // Calculate score based on tolerance and evaluation difference (only if this was a successful move)
          if (trainingMove > 1 && data.prev_move_diff !== null && data.prev_move_diff !== undefined) {
            // Get tolerance threshold based on training level
            var toleranceThreshold = getTolerance(trainingParams.level);

            // Calculate score using formula: t - diff where t is tolerance (absolute value)
            // Convert tolerance to positive value for scoring calculation
            var toleranceValue = Math.abs(toleranceThreshold);
            var diffValue = Math.abs(data.prev_move_diff);
            var moveScore = Math.max(0, toleranceValue - diffValue);
            accumulatedScore += moveScore;
            $('#score-display').text(accumulatedScore);
          }
        }
    }).fail(function() {
        // Hide progress overlay on error
        hideProgress();
        if (trainingActive) {
          stopTraining('Error occurred during training.', 'error');
        }
    });
  }

  // training state
  var trainingActive = false;
  var trainingParams = {};
  var trainingTimer = null;
  var remainingTime = 300; // 5 minutes in seconds
  var accumulatedScore = 0;
  var trainingMove = 0;
  var trainingStartTime = null;
  var trainingEndReason = null;

  // timer functions
  function startTimer() {
    trainingTimer = setInterval(function() {
      remainingTime--;
      updateTimerDisplay();

      if (remainingTime <= 0) {
        stopTraining('Time is up!', 'timeout');
      }
    }, 1000);
  }

  function stopTimer() {
    if (trainingTimer) {
      clearInterval(trainingTimer);
      trainingTimer = null;
    }
  }

  function updateTimerDisplay() {
    var minutes = Math.floor(remainingTime / 60);
    var seconds = remainingTime % 60;
    var timeString = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
    $('#remaining-time').text(timeString);

    // Change color when time is running low
    if (remainingTime <= 60) {
      $('#remaining-time').removeClass('text-primary').addClass('text-danger');
    } else if (remainingTime <= 120) {
      $('#remaining-time').removeClass('text-primary').addClass('text-warning');
    }
  }

  function resetTimer() {
    remainingTime = 300;
    updateTimerDisplay();
    $('#remaining-time').removeClass('text-warning text-danger').addClass('text-primary');
  }

  // Timer freeze/unfreeze functions
  function freezeTimer() {
    if (trainingTimer) {
      clearInterval(trainingTimer);
      trainingTimer = null;
    }
    $('#remaining-time').addClass('timer-frozen');
  }

  function unfreezeTimer() {
    $('#remaining-time').removeClass('timer-frozen').addClass('timer-unfreezing');

    // Remove the unfreezing animation after it completes
    setTimeout(function() {
      $('#remaining-time').removeClass('timer-unfreezing');
    }, 800);

    // Restart the timer
    if (trainingActive && remainingTime > 0) {
      startTimer();
    }
  }

  // Progress tracking variables
  var progressTimer = null;
  var progressStartTime = null;
  var progressDuration = 0;

  // Progress tracking functions
  function showProgress(moveTime) {
    progressDuration = moveTime * 1000;
    progressDuration += 300;

    // Show progress indicator and disable board
    $('#progress-indicator').show();
    $('#chess_board').addClass('board-disabled');

    // Start progress tracking
    progressStartTime = Date.now();
    updateProgress();

    // Update progress every 50ms for smoother animation
    progressTimer = setInterval(updateProgress, 50);
  }

  function hideProgress() {
    // Clear progress timer
    if (progressTimer) {
      clearInterval(progressTimer);
      progressTimer = null;
    }

    // Hide progress indicator and enable board
    $('#progress-indicator').hide();
    $('#chess_board').removeClass('board-disabled');

    // Reset progress bar and time display
    $('#progress-bar-fill').css('width', '0%');
    $('#progress-time').text('0.0s');
  }

  function updateProgress() {
    if (!progressStartTime) return;

    var elapsed = Date.now() - progressStartTime;
    var percentage = Math.min(100, (elapsed / progressDuration) * 100);
    var elapsedSeconds = elapsed / 1000;
    var totalSeconds = progressDuration / 1000;

    // Update progress bar with smooth animation
    $('#progress-bar-fill').css('width', percentage + '%');

    // Display elapsed time and total expected time
    $('#progress-time').text('Waiting ' + (totalSeconds - elapsedSeconds).toFixed(1) + ' seconds...');

    // If we've reached 100% but the request hasn't completed, slow down the progress
    if (percentage >= 100) {
      clearInterval(progressTimer);
      progressTimer = null;

      // Keep the progress at 95% to indicate we're still waiting
      $('#progress-bar-fill').css('width', '95%');
      $('#progress-time').text('Waiting for response...');
    }
  }

  // Clipboard utility function with better Safari support
  function copyToClipboard(text, showSuccessCallback, showFailureCallback) {
    var success = false;

    // Try modern clipboard API first (works in most browsers including Safari 13.1+)
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text).then(function() {
        console.log('FEN copied to clipboard: ' + text);
        success = true;
        if (showSuccessCallback) showSuccessCallback();
      }).catch(function(err) {
        console.error('Failed to copy FEN to clipboard: ', err);
        // Try fallback method
        if (copyToClipboardFallback(text)) {
          if (showSuccessCallback) showSuccessCallback();
        } else {
          if (showFailureCallback) showFailureCallback();
        }
      });
    } else {
      // Use fallback for older browsers or insecure contexts
      if (copyToClipboardFallback(text)) {
        if (showSuccessCallback) showSuccessCallback();
      } else {
        if (showFailureCallback) showFailureCallback();
      }
    }
  }

  // Fallback clipboard function for older browsers
  function copyToClipboardFallback(text) {
    try {
      var textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      // For iOS Safari, we need to set the selection range
      if (navigator.userAgent.match(/ipad|iphone/i)) {
        textArea.setSelectionRange(0, 99999);
      }

      var successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        console.log('FEN copied to clipboard (fallback): ' + text);
        return true;
      } else {
        console.error('Failed to copy FEN to clipboard (fallback)');
        return false;
      }
    } catch (err) {
      console.error('Failed to copy FEN to clipboard (fallback): ', err);
      return false;
    }
  }

  // Function to generate FEN before the last (wrong) move
  function generateFenBeforeLastMove() {
    try {
      // Create a temporary game instance
      var tempGame = new Chess();

      // Load the current game's PGN
      var currentPgn = game.pgn();

      if (!currentPgn || currentPgn.trim() === '') {
        // If no moves have been made, return starting position FEN
        return 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';
      }

      // Load the PGN into the temporary game
      tempGame.load_pgn(currentPgn);

      // Get the move history
      var history = tempGame.history();

      if (history.length === 0) {
        // No moves in history, return starting position FEN
        return 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';
      }

      // Undo the last move to get the position before the wrong move
      tempGame.undo();

      // Return the FEN of this position
      return tempGame.fen();

    } catch (error) {
      console.error('Error generating FEN before last move:', error);
      // Return starting position as fallback
      return 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';
    }
  }

  // Hint handling functions
  function showHint(hintMovesArray, fenBeforeWrongMove) {
    // Freeze the timer
    freezeTimer();

    // Convert UCI moves to SAN format for display
    var sanMoves = convertMovesArrayToSan(hintMovesArray);

    // Display the hint moves
    $('#hint-moves').text(sanMoves);

    // Handle FEN display and copying
    if (fenBeforeWrongMove && fenBeforeWrongMove.trim() !== '') {
      // Show the FEN copy section
      $('#fen-copy-section').show();
      $('#hint-fen-display').val(fenBeforeWrongMove);

      // Hide all notifications initially
      $('#fen-copied-notification').hide();
      $('#fen-copy-failed-notification').hide();

      // Try to copy automatically with callbacks
      copyToClipboard(fenBeforeWrongMove,
        function() {
          // Success callback
          $('#fen-copied-notification').show();
        },
        function() {
          // Failure callback
          $('#fen-copy-failed-notification').show();
        }
      );
    } else {
      // Hide FEN section if no FEN available
      $('#fen-copy-section').hide();
      $('#fen-copied-notification').hide();
      $('#fen-copy-failed-notification').hide();
    }

    // Hide training session, show hint modal
    $('#training-session').hide();
    $('#hint-modal').show();
  }

  function hideHint() {
    // Hide hint modal, show training session
    $('#hint-modal').hide();
    $('#training-session').show();

    // Hide all FEN-related notifications and sections
    $('#fen-copy-section').hide();
    $('#fen-copied-notification').hide();
    $('#fen-copy-failed-notification').hide();

    // Unfreeze the timer
    unfreezeTimer();
  }

  function convertUciToSan(uciMovesString) {
    if (!uciMovesString || uciMovesString.trim() === '') {
      return 'No moves available';
    }

    // Split the UCI moves by space and filter out empty strings
    var uciMoves = uciMovesString.trim().split(' ').filter(function(move) {
      return move.trim() !== '';
    });

    if (uciMoves.length === 0) {
      return 'No moves available';
    }

    var sanMoves = [];

    try {
      // Convert each UCI move to SAN
      for (var i = 0; i < uciMoves.length; i++) {
        try {
          var tempGame = new Chess();
          tempGame.load_pgn(game.pgn());

          // Go back one move to get the position before the player's move
          var history = tempGame.history();
          if (history.length > 0) {
            tempGame.undo();
          }
          var move = tempGame.move(uciMoves[i], { sloppy: true });
          if (move) {
            sanMoves.push(move.san);
          } else {
            sanMoves.push(uciMoves[i]); // Fallback to UCI if conversion fails
          }
        } catch (e) {
          console.log('Error converting UCI move:', uciMoves[i], e);
          sanMoves.push(uciMoves[i]); // Fallback to UCI if conversion fails
        }
      }
    } catch (e) {
      console.log('Error setting up temporary game:', e);
      // If we can't set up the game, just return the UCI moves
      return uciMoves.join(', ');
    }

    return sanMoves.length > 0 ? sanMoves.join(', ') : uciMoves.join(', ');
  }

  function convertMovesArrayToSan(movesArray) {
    if (!movesArray || movesArray.length === 0) {
      return 'No moves available';
    }

    var sanMoves = [];

    try {
      // Convert each UCI move to SAN
      for (var i = 0; i < movesArray.length; i++) {
        try {
          var moveData = movesArray[i];
          var uciMove = moveData[0]; // First element is the UCI move
          var moveDiff = moveData[1]; // Second element is the difference

          var tempGame = new Chess();
          tempGame.load_pgn(game.pgn());

          // Go back one move to get the position before the player's move
          var history = tempGame.history();
          if (history.length > 0) {
            tempGame.undo();
          }
          var move = tempGame.move(uciMove, { sloppy: true });
          if (move) {
            // Include the evaluation difference in the display
            var displayText = move.san;
            sanMoves.push(displayText);
          } else {
            sanMoves.push(uciMove + ' (' + (moveDiff > 0 ? '+' : '') + moveDiff + ')'); // Fallback to UCI if conversion fails
          }
        } catch (e) {
          console.log('Error converting UCI move:', movesArray[i], e);
          var moveData = movesArray[i];
          sanMoves.push(moveData[0] + ' (' + (moveData[1] > 0 ? '+' : '') + moveData[1] + ')'); // Fallback to UCI if conversion fails
        }
      }
    } catch (e) {
      console.log('Error setting up temporary game:', e);
      // If we can't set up the game, just return the UCI moves with their diffs
      return movesArray.map(function(moveData) {
        return moveData[0] + ' (' + (moveData[1] > 0 ? '+' : '') + moveData[1] + ')';
      }).join(', ');
    }

    return sanMoves.length > 0 ? sanMoves.join(', ') : 'No moves available';
  }

  function restartTrainingRound(makeFirstMove = true) {
    // Reset game to starting position using saved PGN to preserve history
    if (trainingParams.startingPgn) {
      game.load_pgn(trainingParams.startingPgn);
    } else {
      game.reset();
    }

    // Update board position
    board.position(game.fen());

    // Set correct orientation
    board.orientation(trainingParams.orientation);

    // Update status
    updateStatus();

    // Bot makes first move after a short delay
    trainingMove = 0;
    if (makeFirstMove) {
      setTimeout(make_move, 800);
    }
  }

  function stopTraining(message, endReason) {
    hideProgress();
    if (currentMoveRequest) {
      currentMoveRequest.abort();
      currentMoveRequest = null;
    }
    trainingActive = false;
    stopTimer();

    // Store end reason for summary
    trainingEndReason = endReason || 'manual';

    // Show training summary instead of just hiding everything
    showTrainingSummary(message);

    // Reset button
    $('#start_training').removeClass('btn-danger').addClass('btn-success');
    $('#start_training').html('🚀 Start Training');

    restartTrainingRound(makeFirstMove=false);
  }

  function showTrainingSummary(message) {
    // Hide all other modals and show summary
    $('#training-settings').hide();
    $('#training-session').hide();
    $('#hint-modal').hide();
    $('#training-summary-modal').show();

    // Remove timer freeze styling
    $('#remaining-time').removeClass('timer-frozen timer-unfreezing');

    // Calculate training duration
    var duration = 'Unknown';
    if (trainingStartTime) {
      var endTime = new Date();
      var durationMs = endTime - trainingStartTime;
      var durationSeconds = Math.floor(durationMs / 1000);
      var minutes = Math.floor(durationSeconds / 60);
      var seconds = durationSeconds % 60;
      duration = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
    }

    // Determine end reason with emoji
    var endReasonDisplay = '';
    switch(trainingEndReason) {
      case 'timeout':
        endReasonDisplay = '⏰ Timeout';
        break;
      case 'surrender':
        endReasonDisplay = '🏳️ Surrender';
        break;
      case 'manual':
        endReasonDisplay = '⏹️ Manual Stop';
        break;
      case 'error':
        endReasonDisplay = '⚠️ Error';
        break;
      default:
        endReasonDisplay = '⏹️ Session Ended';
    }

    // Update summary display
    $('#summary-score').text(accumulatedScore);
    $('#summary-end-reason').text(endReasonDisplay);
    $('#summary-duration').text(duration);
    $('#summary-engine-used').text(trainingParams.engine === 'stockfish' ? 'Stockfish' : 'Leela Chess Zero');
    $('#summary-orientation-used').text(trainingParams.orientation ? trainingParams.orientation.charAt(0).toUpperCase() + trainingParams.orientation.slice(1) : 'Unknown');
    // Format level display for summary
    var summaryLevelDisplay = getLevelDisplayName(trainingParams.level) || 'Unknown';
    $('#summary-level-used').text(summaryLevelDisplay);
  }

  // handle orientation toggle
  $('input[name="orientation"]').on('change', function() {
    var selectedOrientation = $('input[name="orientation"]:checked').val();
    if (selectedOrientation === 'white') {
      board.orientation('white');
    } else {
      board.orientation('black');
    }
  });

  // handle surrender button click
  $('#surrender').on('click', function() {
    stopTraining('Training session ended by surrender.', 'surrender');
  });

  // handle continue after hint button click
  $('#continue-after-hint').on('click', function() {
    hideHint();
    // Subtract 30 seconds from timer for wrong move penalty
    remainingTime = Math.max(0, remainingTime - 30);
    updateTimerDisplay();

    // If time runs out due to penalty, stop training
    if (remainingTime <= 0) {
      stopTraining('Time is up!', 'timeout');
    } else {
      // Otherwise restart the training round
      restartTrainingRound();
    }
  });

  // handle manual FEN copy button click
  $('#copy-fen-button').on('click', function() {
    var fenText = $('#hint-fen-display').val();
    if (fenText && fenText.trim() !== '') {
      // Hide previous notifications
      $('#fen-copied-notification').hide();
      $('#fen-copy-failed-notification').hide();

      // Try to copy with callbacks
      copyToClipboard(fenText,
        function() {
          // Success callback
          $('#fen-copied-notification').show();
          // Hide success message after 3 seconds
          setTimeout(function() {
            $('#fen-copied-notification').fadeOut();
          }, 3000);
        },
        function() {
          // Failure callback
          $('#fen-copy-failed-notification').show();
          // Hide failure message after 5 seconds
          setTimeout(function() {
            $('#fen-copy-failed-notification').fadeOut();
          }, 5000);
        }
      );
    }
  });

  // handle repeat training button click
  $('#repeat-training').on('click', function() {
    // Hide summary modal and show training settings
    $('#training-summary-modal').hide();
    $('#training-settings').show();

    // Automatically start training with the same parameters
    if (trainingParams && Object.keys(trainingParams).length > 0) {
      // Set the form values to match previous training
      $('input[name="orientation"][value="' + trainingParams.orientation + '"]').prop('checked', true);
      $('#move_time').val(trainingParams.moveTime);
      $('#engine').val(trainingParams.engine);
      $('#training_level').val(trainingParams.level);

      // Trigger the start training button click
      setTimeout(function() {
        $('#start_training').click();
      }, 100);
    }
  });

  // handle close summary button click
  $('#close-summary').on('click', function() {
    // Hide summary modal and show training settings
    $('#training-summary-modal').hide();
    $('#training-settings').show();
  });

  // handle start training button click
  $('#start_training').on('click', function() {
    if (!trainingActive) {
      trainingMove = 0;
      // Save training parameters
      var selectedOrientation = $('input[name="orientation"]:checked').val();
      var moveTime = $('#move_time option:selected').val();
      var engine = $('#engine option:selected').val();
      var level = $('#training_level option:selected').val();

        // Validate that it's the bot's turn to move
      var isPlayerWhite = selectedOrientation === 'white';
      var isWhiteToMove = game.turn() === 'w';
      var isPlayerTurn = isPlayerWhite === isWhiteToMove;

      if (isPlayerTurn) {
        alert('Training cannot start - it\'s your turn to move! Please set up a position where the engine should move first.');
        return;
      }

      trainingParams = {
        orientation: selectedOrientation,
        startingPgn: game.pgn(),
        moveTime: moveTime,
        engine: engine,
        level: level
      };

      // Start training
      trainingActive = true;
      accumulatedScore = 0;
      trainingStartTime = new Date();
      resetTimer();

      // Hide settings, show session
      $('#training-settings').hide();
      $('#training-session').show();

      // Update session info
      $('#session-orientation').text(selectedOrientation.charAt(0).toUpperCase() + selectedOrientation.slice(1));
      $('#session-engine').text(engine === 'stockfish' ? 'Stockfish' : 'Leela Chess Zero');
      // Format move time display
      var moveTimeDisplay;
      if (moveTime === 'instant') {
        moveTimeDisplay = 'Instant';
      } else if (moveTime === '60') {
        moveTimeDisplay = '1 minute';
      } else {
        moveTimeDisplay = moveTime + ' sec';
      }
      $('#session-move-time').text(moveTimeDisplay);
      // Format level display
      var levelDisplay = getLevelDisplayName(level);
      $('#session-level').text(levelDisplay);
      $('#score-display').text(accumulatedScore);

      // Set board orientation
      board.orientation(selectedOrientation);

      updateStatus();
      startTimer();

      // Bot always makes the first move in training
      make_move();
    } else {
      // Stop training
      stopTraining('', 'manual');
    }
  });

  // handle reset board button click
  $('#reset_board').on('click', function() {
    // reset board state to starting position
    game.reset();

    // set initial board position
    board.position('start');

    // preserve current orientation (don't reset orientation radio buttons)

    // stop training if active
    if (trainingActive) {
      stopTraining('', 'manual');
    }

    updateStatus();
  });
  
  // handle set FEN button click
  $('#set_fen').on('click', function() {
    // set user FEN
    var fenValue = $('#fen').val().trim();

    if (fenValue === '') {
      // Empty FEN means starting position
      game.reset();
      board.position('start');
      updateStatus();
    } else {
      // FEN parsed
      if (game.load(fenValue)) {
        // set board position
        board.position(game.fen());
        updateStatus();
      } else {
        // FEN is not parsed
        alert('Invalid FEN position!');
      }
    }
  });

  // GUI board & game state variables
  var board = null;
  var game = new Chess();
  var $status = $('#status');
  var $fen = $('#fen');

  // on picking up a piece
  function onDragStart (source, piece, position, orientation) {
    // do not pick up pieces if the game is over
    // if (game.game_over()) return false

    var wrong_color = ((game.turn() === 'w' && piece.search(/^b/) !== -1) ||
        (game.turn() === 'b' && piece.search(/^w/) !== -1))
    if (wrong_color && trainingActive) {
      return false
    }
  }

  // on dropping piece
  function onDrop (source, target) {
    // see if the move is legal
    var move = game.move({
      from: source,
      to: target,
      promotion: 'q' // NOTE: always promote to a queen for example simplicity
    })

    // illegal move
    if (move === null) return 'snapback'

    // update game status
    updateStatus();

    // if training is active and game is not over, make computer move
    if (trainingActive) {
      make_move(); // Small delay for better UI
    }
  }

  // update the board position after the piece snap
  // for castling, en passant, pawn promotion
  function onSnapEnd () {
    board.position(game.fen())
  }

  // update game status
  function updateStatus () {
    var status = ''

    var moveColor = 'White'
    if (game.turn() === 'b') {
      moveColor = 'Black'
    }

    // checkmate?
    if (game.in_checkmate()) {
      status = 'Game over, ' + moveColor + ' is in checkmate.'
    }

    // draw?
    else if (game.in_draw()) {
      status = 'Game over, drawn position'
    }

    // game still on
    else {
      status = moveColor + ' to move'

      // check?
      if (game.in_check()) {
        status += ', ' + moveColor + ' is in check'
      }
    }

    // update DOM elements
    $status.html(status)
    $fen.val(game.fen())
  }

  // chess board configuration
  var config = {
    draggable: true,
    position: 'start',
    onDragStart: onDragStart,
    onDrop: onDrop,
    onSnapEnd: onSnapEnd
  }
  
  // create chess board widget instance
  board = Chessboard('chess_board', config)

  // populate training level options using centralized configuration
  populateTrainingLevelOptions();

  // prevent scrolling on touch devices
  $('#chess_board').on('scroll touchmove touchend touchstart contextmenu', function(e) {
    e.preventDefault();
  });

  // update game status
  updateStatus();
</script>
