[project]
name = "debut-memorizer"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "diskcache>=5.6.3",
    "flask>=3.1.1",
    "flask-pydantic>=0.13.1",
    "flask-pymongo>=3.0.1",
    "gunicorn>=23.0.0",
    "ipykernel>=6.30.0",
    "ipython>=9.4.0",
    "jupyter>=1.1.1",
    "lczero-bindings>=0.1.0",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.7",
    "python-chess>=1.999",
    "python-dotenv>=1.1.1",
    "ruff>=0.12.8",
    "sqlalchemy>=2.0.42",
    "tqdm>=4.67.1",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.ruff]
line-length = 150
src = ["src"]

[tool.ruff.lint]
extend-select = ["I"]


[tool.ruff.lint.isort]
known-first-party = ["src"]
force-single-line = true
force-sort-within-sections = true
